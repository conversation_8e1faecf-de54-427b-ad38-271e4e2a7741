// 测试代理服务器的脚本

const API_KEY = '9d9113ca-c9a0-49bc-b06a-62b5973a7b6a';
const PROXY_URL = 'http://localhost:3000';

async function testChatAPI() {
  console.log('🧪 测试对话 API...');
  
  try {
    const response = await fetch(`${PROXY_URL}/api/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'doubao-lite-4k',
        messages: [
          {
            role: 'user',
            content: '你好，请简单介绍一下自己。'
          }
        ],
        stream: false
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 对话 API 测试成功');
      console.log('📝 响应内容:', data.choices[0].message.content.substring(0, 100) + '...');
    } else {
      console.log('❌ 对话 API 测试失败');
      console.log('状态码:', response.status);
      console.log('错误信息:', await response.text());
    }
  } catch (error) {
    console.log('❌ 对话 API 测试出错:', error.message);
  }
}

async function testImageAPI() {
  console.log('\n🎨 测试图片生成 API...');
  
  try {
    const response = await fetch(`${PROXY_URL}/api/images/generations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'doubao-seedream-3-0-t2i-250415',
        prompt: '一只可爱的小猫咪在花园里玩耍',
        response_format: 'url',
        size: '1024x1024'
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 图片生成 API 测试成功');
      console.log('🖼️  图片 URL:', data.data[0].url);
    } else {
      console.log('❌ 图片生成 API 测试失败');
      console.log('状态码:', response.status);
      console.log('错误信息:', await response.text());
    }
  } catch (error) {
    console.log('❌ 图片生成 API 测试出错:', error.message);
  }
}

async function testHealthCheck() {
  console.log('🏥 测试健康检查...');
  
  try {
    const response = await fetch(`${PROXY_URL}/health`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 健康检查通过');
      console.log('📊 状态:', data.status);
      console.log('⏰ 时间:', data.timestamp);
    } else {
      console.log('❌ 健康检查失败');
    }
  } catch (error) {
    console.log('❌ 健康检查出错:', error.message);
  }
}

async function runTests() {
  console.log('🚀 开始测试代理服务器...');
  console.log('================================\n');
  
  await testHealthCheck();
  await testChatAPI();
  await testImageAPI();
  
  console.log('\n================================');
  console.log('🎉 测试完成！');
}

// 运行测试
runTests().catch(console.error);
