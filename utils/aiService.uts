const ARK_API_KEY = 'YOUR_ARK_API_KEY'; // 请替换为您的实际 API 密钥
const PROXY_BASE_URL = 'http://localhost:3000'; // 指向您的代理服务器地址

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface ChatCompletionResponse {
  choices: Array<{
    message: ChatMessage;
  }>;
}

interface ImageGenerationResponse {
  data: Array<{
    url: string;
  }>;
}

export async function generateStory(messages: ChatMessage[], model: string = "doubao-lite-4k"): Promise<string> {
  try {
    const response = await uni.request({
      url: `${PROXY_BASE_URL}/api/chat/completions`, // 修改为代理服务器地址
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ARK_API_KEY}`
      },
      data: {
        model: model,
        messages: messages,
        stream: false
      }
    });

    if (response.statusCode === 200 && response.data) {
      const data = response.data as ChatCompletionResponse;
      return data.choices[0].message.content;
    } else {
      console.error('Error generating story:', response);
      return 'Failed to generate story.';
    }
  } catch (error) {
    console.error('Network error generating story:', error);
    return 'Network error.';
  }
}

export async function generateImage(prompt: string, model: string = "doubao-seedream-3-0-t2i-250415"): Promise<string> {
  try {
    const response = await uni.request({
      url: `${PROXY_BASE_URL}/api/images/generations`, // 修改为代理服务器地址
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ARK_API_KEY}`
      },
      data: {
        model: model,
        prompt: prompt,
        response_format: "url",
        size: "1024x1024"
      }
    });

    if (response.statusCode === 200 && response.data) {
      const data = response.data as ImageGenerationResponse;
      return data.data[0].url;
    } else {
      console.error('Error generating image:', response);
      return ''; // Return empty string on error
    }
  } catch (error) {
    console.error('Network error generating image:', error);
    return ''; // Return empty string on error
  }
}