<template>
  <view class="start-container">
    <text class="title">选择你的故事</text>

    <view class="section">
      <text class="section-title">你的性别：</text>
      <radio-group @change="genderChange" class="radio-group">
        <label class="radio-label">
          <radio value="男性" :checked="selectedGender === '男性'" />男性
        </label>
        <label class="radio-label">
          <radio value="女性" :checked="selectedGender === '女性'" />女性
        </label>
      </radio-group>
    </view>

    <view class="section">
      <text class="section-title">故事类型：</text>
      <picker @change="storyTypeChange" :value="storyTypes.indexOf(selectedStoryType)" :range="storyTypes" class="picker">
        <view class="picker-text">{{ selectedStoryType || '请选择故事类型' }}</view>
      </picker>
    </view>

    <button @click="startStory" class="start-button">开始故事</button>
  </view>
</template>

<script>
import { ref } from 'vue';

export default {
  setup() {
    const selectedGender = ref('男性'); // Default to male
    const storyTypes = ref([
      '奇幻冒险',
      '科幻探索',
      '武侠江湖',
      '都市言情',
      '悬疑推理',
      '历史传奇',
      '校园青春',
      '童话寓言'
    ]);
    const selectedStoryType = ref(storyTypes.value[0]); // Default to first type

    const genderChange = (e) => {
      selectedGender.value = e.detail.value;
    };

    const storyTypeChange = (e) => {
      selectedStoryType.value = storyTypes.value[e.detail.value];
    };

    const startStory = () => {
      if (!selectedGender.value || !selectedStoryType.value) {
        uni.showToast({
          title: '请选择性别和故事类型',
          icon: 'none'
        });
        return;
      }
      uni.navigateTo({
        url: `/pages/story/index?gender=${selectedGender.value}&type=${selectedStoryType.value}`
      });
    };

    return {
      selectedGender,
      storyTypes,
      selectedStoryType,
      genderChange,
      storyTypeChange,
      startStory,
    };
  },
};
</script>

<style>
.start-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 40px;
  color: #333;
}

.section {
  width: 80%;
  margin-bottom: 30px;
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #555;
}

.radio-group {
  display: flex;
  justify-content: space-around;
  width: 100%;
}

.radio-label {
  font-size: 18px;
  color: #666;
}

.picker {
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  font-size: 18px;
  color: #666;
  text-align: center;
}

.picker-text {
  line-height: 1.5;
}

.start-button {
  background-color: #007aff;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 10px;
  font-size: 20px;
  cursor: pointer;
  margin-top: 30px;
  width: 80%;
  box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
}
</style>
