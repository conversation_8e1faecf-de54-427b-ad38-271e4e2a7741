<template>
  <view class="story-container">
    <image :src="backgroundImage" mode="aspectFill" class="background-image"></image>
    <view class="story-content">
      <text class="story-text">{{ currentStory }}</text>
      <view class="options-container">
        <button v-for="(option, index) in options" :key="index" @click="selectOption(option)" class="option-button">{{ option }}</button>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue'; // 移除 onLoad 的导入
import { generateStory, generateImage } from '../../utils/aiService';

export default {
  setup() {
    const currentStory = ref('Loading story...');
    const options = ref([]);
    const backgroundImage = ref('');
    const conversationHistory = ref([]); // To maintain dialogue context

    let userGender = ''; // Will be set from query params
    let storyType = '';  // Will be set from query params

    // Helper function to parse story and options from AI response
    const parseStoryAndOptions = (aiResponse) => {
      const lines = aiResponse.split('\n').map(line => line.trim()).filter(line => line.length > 0);
      let storyText = [];
      let parsedOptions = [];

      for (const line of lines) {
        if (line.startsWith('选项1:') || line.startsWith('选项2:') || line.startsWith('选项3:')) {
          parsedOptions.push(line.substring(line.indexOf(':') + 1).trim());
        } else {
          storyText.push(line);
        }
      }
      return {
        story: storyText.join('\n'),
        options: parsedOptions,
      };
    };

    const fetchStoryAndImage = async (userMessageContent = '') => {
      // Add user message to history if it's a follow-up
      if (userMessageContent) {
        conversationHistory.value.push({ role: 'user', content: userMessageContent });
      }

      // Construct the prompt for AI
      let promptMessages = [...conversationHistory.value];
      if (promptMessages.length === 0) {
        // Initial prompt
        promptMessages.push({
          role: 'system',
          content: '你是一个互动故事的创作者。请根据用户的选择，继续故事，并提供三个不同的选项。每个选项以“选项1:”、“选项2:”、“选项3:”开头。'
        });
        promptMessages.push({
          role: 'user',
          content: `请创作一个关于${userGender}的${storyType}互动故事的开头，并提供三个选项。`
        });
      } else {
        // Follow-up prompt
        promptMessages.push({
          role: 'user',
          content: `我选择了"${userMessageContent}"。请继续故事，并提供三个新的选项。`
        });
      }

      currentStory.value = 'Generating story...';
      options.value = [];

      const aiResponse = await generateStory(promptMessages);
      conversationHistory.value.push({ role: 'assistant', content: aiResponse }); // Add AI's response to history

      const { story, options: newOptions } = parseStoryAndOptions(aiResponse);
      currentStory.value = story;
      options.value = newOptions;

      // Generate background image based on story content
      const imagePrompt = `A beautiful scene depicting: ${story.substring(0, Math.min(story.length, 50))}...`; // Use first 50 chars as prompt
      backgroundImage.value = await generateImage(imagePrompt);
    };

    const selectOption = async (option) => {
      console.log('Selected option:', option);
      await fetchStoryAndImage(option);
    };

    // onLoad lifecycle hook - no import needed, directly available in setup
    onLoad((options) => {
      if (options.gender) {
        userGender = options.gender;
      }
      if (options.type) {
        storyType = options.type;
      }
      fetchStoryAndImage(); // Start fetching story after parameters are set
    });

    return {
      currentStory,
      options,
      backgroundImage,
      selectOption,
    };
  },
};
</script>

<style>
.story-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.story-content {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 20px;
  margin-bottom: 50px;
  border-radius: 10px;
  text-align: center;
  max-width: 90%;
}

.story-text {
  font-size: 18px;
  margin-bottom: 20px;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-button {
  background-color: #007aff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
}
</style>